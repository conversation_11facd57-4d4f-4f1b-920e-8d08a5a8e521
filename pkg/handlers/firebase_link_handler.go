package handlers

import (
	"errors"

	"gofr.dev/pkg/gofr"
	"gofr.dev/pkg/gofr/http/response"
)

const fallbackUrl = "https://stagedates.com"

var (
	ErrDbResultsNotFound                       = errors.New("FirebaseLinkHandler(): No results found - failed to retrieve target URL")
	ErrShortLinkIdentifierUrlParameterNotFound = errors.New("FirebaseLinkHandler(): short link identifier url parameter required but not found")
	fallbackRedirect                           = response.Redirect{
		URL: fallbackUrl,
	}
)

type FirebaseLink struct {
	ID          string `json:"id"`
	OriginalURL string `json:"original_url"` // DEPRECATED: use SrcURL instead
}

// FirebaseLinkHandler handles requests to the root (/) route for Firebase links
func FirebaseLinkHandler(ctx *gofr.Context) (interface{}, error) {
	// Get the identifier from the URL path
	identifier := ctx.PathParam("identifier")

	if identifier == "" {
		ctx.Logf(ErrShortLinkIdentifierUrlParameterNotFound.Error())
		return ErrShortLinkIdentifierUrlParameterNotFound, nil
	}

	if identifier == "health" {
		return "ok", nil
	}

	query := "SELECT id, original_url FROM shortlink.link WHERE path = $1"
	rows, err := ctx.SQL.QueryContext(ctx, query, identifier)
	defer rows.Close()

	if err != nil {
		ctx.Logf("FirebaseLinkHandler(): %v", err)
		return ErrDbResultsNotFound, nil
	}

	var results []FirebaseLink
	for rows.Next() {
		var link FirebaseLink
		if err := rows.Scan(&link.ID, &link.OriginalURL); err != nil {
			ctx.Logf("FirebaseLinkHandler(): %v", err)
			return fallbackRedirect, nil
		}
		results = append(results, link)
		ctx.Logf("FirebaseLinkHandler(): appending: %v", link)
		break // Only process the first row since we expect only one result
	}

	if len(results) > 0 {
		targetUrl := results[0].OriginalURL

		if err := validateURL(targetUrl); err != nil {
			ctx.Logf(ErrInvalidUrl.Error())
			return ErrInvalidUrl, err
		}

		ctx.Logf("FirebaseLinkHandler(): Redirecting to URL", "original_url", targetUrl)
		return response.Redirect{
			URL: targetUrl,
		}, nil
	} else {
		ctx.Logf(ErrDbResultsNotFound.Error())
		return ErrDbResultsNotFound, nil
	}
}
