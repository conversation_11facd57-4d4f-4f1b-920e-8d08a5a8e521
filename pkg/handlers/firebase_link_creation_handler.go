package handlers

import (
	"crypto/rand"
	"encoding/hex"
	"errors"
	"net/url"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/stagedates/shortlink-service/pkg/validators"
	"gofr.dev/pkg/gofr"
)

type DynamicLink struct {
	ID         uuid.UUID `json:"id"`
	SrcURL     string    `json:"shortlink"`
	TargetURL  string    `json:"targetUrl"`
	Path       string    `json:"path"`
	InsertedAt time.Time `json:"insertedAt"`
	UpdatedAt  time.Time `json:"updatedAt"`
}

func generateRandomPath() string {
	bytes := make([]byte, 3) // 6 character hex string
	rand.Read(bytes)
	return hex.EncodeToString(bytes)
}

// FirebaseLinkCreationHandler creates a new link in the database
// based on the firebase dynamic link specification found here:
// https://firebase.google.com/docs/dynamic-links/rest
// Target url example "https://stagedates.com/events/star-fm-rooftop-party-nhow-berlin-20250726-vDO1Q"
func FirebaseLinkCreationHandler(app *gofr.App) func(*gofr.Context) (any, error) {
	return func(ctx *gofr.Context) (any, error) {
		var (
			link      DynamicLink
			_srcURL   string
			srcURL    string
			targetUrl string
			path      string
		)

		if err := ctx.Bind(&link); err != nil {
			return nil, err
		}

		ctx.Logger.Debugf("inserting link: %+v", link)

		// Extract API key from context
		requestApiKeyRaw := ctx.Value("RequestApiKey")

		// Convert to string safely
		requestApiKey := ""
		if requestApiKeyRaw != nil {
			if apiKeyStr, ok := requestApiKeyRaw.(string); ok {
				requestApiKey = apiKeyStr
			}
		}

		if !validators.ApiKeyValidator(app, requestApiKey) {
			ctx.Logger.Warn("Unauthorized: Invalid or missing API key")
			return nil, errors.New("unauthorized: invalid or missing API key")
		}

		// the source_url is the shortlink that the user will be redirected from and might prefer during creation
		if len(link.SrcURL) > 0 {
			_srcURL = link.SrcURL
			if parsedURL, err := url.Parse(link.SrcURL); err == nil && parsedURL.Path != "" {
				ctx.Logger.Info("Source URL path extracted from provided srcUrl: %s", parsedURL.Path)
				path = strings.TrimPrefix(parsedURL.Path, "/")
			}
		} else {
			path = generateRandomPath()
			baseURL := app.Config.GetOrDefault("SHORTLINK_BASE_URL", "https://stagedates.com/")
			_srcURL = strings.TrimRight(baseURL, "/") + "/" + path
		}

		srcURL = _srcURL
		targetUrl = link.TargetURL
		insertedAt := time.Now()
		updatedAt := insertedAt

		ctx.Logger.Infof("inserting link: %+v", targetUrl)

		query := "INSERT INTO shortlink.link (src_url, target_url, path, inserted_at, updated_at) "
		query = query + "VALUES ($1, $2, $3, $4, $5) RETURNING id"

		var linkID uuid.UUID
		err := ctx.SQL.QueryRowContext(ctx, query, srcURL, targetUrl, path, insertedAt, updatedAt).Scan(&linkID)

		if err != nil {
			ctx.Logger.Errorf("error inserting link: %+v", err)
			return nil, err
		} else {
			responseLink := DynamicLink{
				ID:         linkID,
				SrcURL:     srcURL,
				TargetURL:  targetUrl,
				Path:       path,
				InsertedAt: insertedAt,
				UpdatedAt:  updatedAt,
			}

			ctx.Logger.Infof("link inserted: %+v", responseLink)
			return responseLink, nil
		}

	}
}
