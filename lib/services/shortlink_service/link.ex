defmodule ExServiceClient.Services.ShortlinkService.Link do
  @moduledoc """
  Represents a link response from the shortlink service.

  This struct contains the fields returned by the shortlink service API
  when creating or retrieving links.
  """

  use ExServiceClient.Cacheable
  use ExServiceClient.Clients.BaseClient

  alias ExServiceClient.Services.ShortlinkService.Link
  alias ExServiceClient.Util.DateTimeUtil

  @opts [endpoint: :shortlink]

  defstruct [
    :id,
    :srcUrl,
    :targetUrl,
    :path,
    :insertedAt,
    :updatedAt
  ]

  @type t :: %__MODULE__{
          id: String.t(),
          srcUrl: String.t(),
          targetUrl: String.t(),
          path: String.t(),
          insertedAt: DateTime.t(),
          updatedAt: DateTime.t()
        }

  defimpl Poison.Decoder, for: Link do
    @doc """
    Decodes a JSON response into a Link struct, parsing ISO8601 datetime strings
    into Elixir DateTime structs.
    """
    def decode(%Link{insertedAt: inserted_at, updatedAt: updated_at} = link, _options) do
      %{link | insertedAt: DateTimeUtil.parse_iso8601(inserted_at), updatedAt: DateTimeUtil.parse_iso8601(updated_at)}
    end
  end

  @spec create(map()) :: {:ok, t()} | {:error, map()}
  def create(params) do
    @opts
    |> client()
    |> Tesla.post("/links", params)
    |> parse_response(&decode!/1)
  end

  defp decode!(data) do
    Poison.decode!(data, as: %Link{})
  end
end
